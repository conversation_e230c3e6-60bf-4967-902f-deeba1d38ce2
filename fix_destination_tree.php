<?php
/**
 * <PERSON><PERSON><PERSON> to recover the corrupted Destination tree structure
 * This will rebuild the lft/rght values based on parent_id relationships
 */

// Bootstrap CakePHP
$_SERVER['REQUEST_URI'] = '/';
$_SERVER['HTTP_HOST'] = 'localhost';

// Set up paths
define('DS', DIRECTORY_SEPARATOR);
define('ROOT', dirname(__FILE__));
define('APP_DIR', 'app');
define('CAKE_CORE_INCLUDE_PATH', ROOT . DS . 'cake');
define('WEBROOT_DIR', 'webroot');
define('WWW_ROOT', ROOT . DS . APP_DIR . DS . WEBROOT_DIR . DS);

// Include the dispatcher to bootstrap CakePHP
$_GET['url'] = '';
ob_start();
include ROOT . DS . APP_DIR . DS . 'webroot' . DS . 'index.php';
ob_end_clean();

// Create destination model instance
$destination = new Destination();

echo "=== Destination Tree Recovery Script ===\n\n";

// First, let's check the current state
echo "Checking current tree structure...\n";
$corruptedRecords = $destination->find('all', array(
    'conditions' => 'Destination.lft >= Destination.rght',
    'fields' => array('id', 'parent_id', 'name', 'lft', 'rght'),
    'recursive' => -1,
    'order' => 'Destination.lft'
));

echo "Found " . count($corruptedRecords) . " records with corrupted lft/rght values:\n";
foreach ($corruptedRecords as $record) {
    printf("ID: %d, Name: %s, Parent: %s, lft: %d, rght: %d\n",
        $record['Destination']['id'],
        $record['Destination']['name'],
        $record['Destination']['parent_id'] ?: 'NULL',
        $record['Destination']['lft'],
        $record['Destination']['rght']
    );
}

echo "\n";

// Check for specific problematic records
echo "Checking Worldwide Holidays (ID: 555)...\n";
$worldwideHolidays = $destination->findById(555);
if ($worldwideHolidays) {
    printf("Current state: ID: %d, Parent: %s, lft: %d, rght: %d\n",
        $worldwideHolidays['Destination']['id'],
        $worldwideHolidays['Destination']['parent_id'] ?: 'NULL',
        $worldwideHolidays['Destination']['lft'],
        $worldwideHolidays['Destination']['rght']
    );
}

echo "\nChecking Far & Wide (ID: 718)...\n";
$farAndWide = $destination->findById(718);
if ($farAndWide) {
    printf("Current state: ID: %d, Parent: %s, lft: %d, rght: %d\n",
        $farAndWide['Destination']['id'],
        $farAndWide['Destination']['parent_id'] ?: 'NULL',
        $farAndWide['Destination']['lft'],
        $farAndWide['Destination']['rght']
    );
}

echo "\n=== Running Tree Recovery ===\n";

try {
    // Run the recover method
    $result = $destination->recover();

    if ($result) {
        echo "✓ Tree recovery completed successfully!\n\n";

        // Check the results
        echo "Verifying recovery...\n";
        $stillCorrupted = $destination->find('all', array(
            'conditions' => 'Destination.lft >= Destination.rght',
            'fields' => array('id', 'parent_id', 'name', 'lft', 'rght'),
            'recursive' => -1
        ));

        if (empty($stillCorrupted)) {
            echo "✓ No corrupted records found after recovery!\n";
        } else {
            echo "⚠ Still found " . count($stillCorrupted) . " corrupted records:\n";
            foreach ($stillCorrupted as $record) {
                printf("ID: %d, Name: %s, lft: %d, rght: %d\n",
                    $record['Destination']['id'],
                    $record['Destination']['name'],
                    $record['Destination']['lft'],
                    $record['Destination']['rght']
                );
            }
        }

        // Check specific records again
        echo "\nChecking Worldwide Holidays after recovery...\n";
        $worldwideHolidays = $destination->findById(555);
        if ($worldwideHolidays) {
            printf("After recovery: ID: %d, Parent: %s, lft: %d, rght: %d\n",
                $worldwideHolidays['Destination']['id'],
                $worldwideHolidays['Destination']['parent_id'] ?: 'NULL',
                $worldwideHolidays['Destination']['lft'],
                $worldwideHolidays['Destination']['rght']
            );

            // Test the breadcrumb path
            echo "Testing breadcrumb path...\n";
            $breadcrumbData = $destination->breadcrumbData(555, false);
            if ($breadcrumbData && isset($breadcrumbData['breadcrumbs'])) {
                echo "Breadcrumb path:\n";
                foreach ($breadcrumbData['breadcrumbs'] as $crumb) {
                    echo "  - " . $crumb['Destination']['name'] . "\n";
                }
            }
        }

    } else {
        echo "✗ Tree recovery failed!\n";
    }

} catch (Exception $e) {
    echo "✗ Error during tree recovery: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Recovery Complete ===\n";
?>
