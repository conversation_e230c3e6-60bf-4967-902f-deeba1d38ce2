<?php

class DestinationsController extends AppController {

  var $name = 'Destinations';
  var $components = array('Section', 'Navigation');

  function beforeFilter() {
    parent::beforeFilter();
    if ($this->Auth->allowedActions <> array('*')) {
      $this->Auth->allowedActions = array_merge($this->Auth->allowedActions, array('healthcheck', 'webadmin_recover_tree'));
    }
  }


  function webadmin_edit($id = null) {

    parent::webadmin_edit($id);

    $mapData = $this->Destination->getMapData($id, 'editor');

    $this->set(compact('mapData'));

  }

  function webadmin_recover_tree() {

    echo "<h1>Destination Tree Recovery</h1>";

    // Check current state
    echo "<h2>Checking current tree structure...</h2>";
    $corruptedRecords = $this->Destination->find('all', array(
        'conditions' => 'Destination.lft >= Destination.rght',
        'fields' => array('id', 'parent_id', 'name', 'lft', 'rght'),
        'recursive' => -1,
        'order' => 'Destination.lft'
    ));

    echo "<p>Found " . count($corruptedRecords) . " records with corrupted lft/rght values:</p>";
    if (!empty($corruptedRecords)) {
        echo "<ul>";
        foreach ($corruptedRecords as $record) {
            printf("<li>ID: %d, Name: %s, Parent: %s, lft: %d, rght: %d</li>",
                $record['Destination']['id'],
                $record['Destination']['name'],
                $record['Destination']['parent_id'] ?: 'NULL',
                $record['Destination']['lft'],
                $record['Destination']['rght']
            );
        }
        echo "</ul>";
    }

    // Check specific records
    echo "<h3>Checking Worldwide Holidays (ID: 555)...</h3>";
    $worldwideHolidays = $this->Destination->findById(555);
    if ($worldwideHolidays) {
        printf("<p>Current state: ID: %d, Parent: %s, lft: %d, rght: %d</p>",
            $worldwideHolidays['Destination']['id'],
            $worldwideHolidays['Destination']['parent_id'] ?: 'NULL',
            $worldwideHolidays['Destination']['lft'],
            $worldwideHolidays['Destination']['rght']
        );
    }

    echo "<h2>Running Tree Recovery...</h2>";

    try {
        // Run the recover method
        $result = $this->Destination->recover();

        if ($result) {
            echo "<p style='color: green;'>✓ Tree recovery completed successfully!</p>";

            // Check the results
            echo "<h3>Verifying recovery...</h3>";
            $stillCorrupted = $this->Destination->find('all', array(
                'conditions' => 'Destination.lft >= Destination.rght',
                'fields' => array('id', 'parent_id', 'name', 'lft', 'rght'),
                'recursive' => -1
            ));

            if (empty($stillCorrupted)) {
                echo "<p style='color: green;'>✓ No corrupted records found after recovery!</p>";
            } else {
                echo "<p style='color: orange;'>⚠ Still found " . count($stillCorrupted) . " corrupted records:</p>";
                echo "<ul>";
                foreach ($stillCorrupted as $record) {
                    printf("<li>ID: %d, Name: %s, lft: %d, rght: %d</li>",
                        $record['Destination']['id'],
                        $record['Destination']['name'],
                        $record['Destination']['lft'],
                        $record['Destination']['rght']
                    );
                }
                echo "</ul>";
            }

            // Check specific records again
            echo "<h3>Checking Worldwide Holidays after recovery...</h3>";
            $worldwideHolidays = $this->Destination->findById(555);
            if ($worldwideHolidays) {
                printf("<p>After recovery: ID: %d, Parent: %s, lft: %d, rght: %d</p>",
                    $worldwideHolidays['Destination']['id'],
                    $worldwideHolidays['Destination']['parent_id'] ?: 'NULL',
                    $worldwideHolidays['Destination']['lft'],
                    $worldwideHolidays['Destination']['rght']
                );

                // Test the breadcrumb path
                echo "<h4>Testing breadcrumb path...</h4>";
                $breadcrumbData = $this->Destination->breadcrumbData(555, false);
                if ($breadcrumbData && isset($breadcrumbData['breadcrumbs'])) {
                    echo "<p>Breadcrumb path:</p><ul>";
                    foreach ($breadcrumbData['breadcrumbs'] as $crumb) {
                        echo "<li>" . $crumb['Destination']['name'] . "</li>";
                    }
                    echo "</ul>";
                }
            }

        } else {
            echo "<p style='color: red;'>✗ Tree recovery failed!</p>";
        }

    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error during tree recovery: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }

    echo "<h2>Recovery Complete</h2>";

    exit; // Don't render a view
  }

  function view() {

    //This controller uses the section component. The component already
    //returns the destination data and calls a 404 if not found. So in
    //theory it should never reach here if the data is empty, but just
    //in case, here's another check
    if (empty($this->sectionData['Destination'])) {
      $this->cakeError('error404');
    }

    $destination = $this->sectionData;

    $destinationActivities = $this->_findActivities($this->sectionId);

    $related = $this->_findHolidayTypes($this->sectionId);

    $this->set(compact('destination', 'destinationActivities', 'related', 'breadcrumbs'));
  }

  function healthcheck() {
    $this->view();
  }

  /**
   * Returns the children of a destination
   */
  protected function _findChildren($sectionId) {
    $findChildren = function() use ($sectionId) {
      return @$this->Destination->children(
        $sectionId, true, null, null, null, null, 0
      );
    };

    return $this->cacher(implode('_', array(
      'destination', $sectionId,  'children'
    )), $findChildren);
  }

  /**
   * Returns a destinations related activities
   */
  protected function _findActivities($sectionId) {
    $findActivities = function() use ($sectionId) {
      return ClassRegistry::init('Activity')->getActivitiesByDestination($sectionId);
    };

    return $this->cacher(implode('_', array(
      'destination', $sectionId, 'activities'
    )), $findActivities);
  }

  /**
   * Returns a destinations related holiday types
   */
  protected function _findHolidayTypes($sectionId) {
    $findHolidayTypes = function() use ($sectionId) {
      return ClassRegistry::init('HolidayType')->getHolidayTypesOnDestination($sectionId);
    };

    return $this->cacher(implode('_', array(
      'destination', $sectionId, 'holiday_types'
    )), $findHolidayTypes);
  }
}

?>
