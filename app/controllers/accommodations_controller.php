<?php
class AccommodationsController extends AppController
{
    public $name = 'Accommodations';
    public $components = array('Section');

    public function index()
    {
        $this->paginate['Accommodation'] = array(
            'limit' => 24,
            'recursive' => 1,
            'contain' => array(
                'Image',
            ),
            'order' => 'name',
        );

        if ($this->params['section']) {
            $sectionModel = $this->Section->sectionSettings[$this->params['section']]['model'];

            $sectionForeignKey = Inflector::underscore($sectionModel).'_id';

            $joinTable = $this->{$this->modelClass}->hasAndBelongsToMany[$sectionModel]['joinTable'];

            $with = Inflector::classify($joinTable);

            $this->Accommodation->bindModel(array(
                'hasOne' => array(
                    $with
                ),
            ), false);

            $this->paginate['Accommodation']['contain'][] = $with;

            $this->paginate['Accommodation']['order'] = $with.'.order';

            $this->paginate['Accommodation']['conditions'] = array(
                $with.'.'.$sectionForeignKey => $this->sectionId
            );
        }

        $accommodations = $this->paginate('Accommodation');

        $this->_canonicalUrlForPaginated();

        $this->set(compact('accommodations'));

        $this->_setMeta('Accommodation');
    }

    public function view()
    {
        $accommodation = $this->_findBySlug($this->params['accommodation_slug']);

        if (empty($accommodation)) {
            $this->cakeError('error404');
        }

        $this->_canonicalUrl(array(
            'accommodation_slug' => $this->params['accommodation_slug'],
            'section'            => 'destinations'
        ));

        $this->set(compact('accommodation'));

        $this->_setMeta($accommodation['Accommodation']);

        $mapData = $this->Accommodation->getMapData($accommodation);

        $this->set(compact('mapData'));
    }

    public function webadmin_edit($id = null)
    {
        if ($this->{$this->modelClass}->Behaviors->enabled('Publishable')) {
            $this->{$this->modelClass}->Behaviors->disable('Publishable');
        }


        extract($this->{$this->modelClass}->getInfo());

        if (!$id && empty($this->data)) {
            $this->Session->setFlash(__("Invalid $singularHumanName.", true), 'webadmin_flash_bad');
            $this->History->back(0);
        }
        if (!empty($this->data)) {
            if ($this->$modelClass->save($this->data)) {
                $this->Session->setFlash(__("The $singularHumanName has been saved", true), 'webadmin_flash_good');
                if (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save', true)) {
                    // "Save" button should stay on the same page - no redirect needed
                } else {
                    // "Save and Go Back" button - use history to go back to previous page
                    $this->History->back();
                    return; // Stop execution after redirect
                }
            } else {
                $this->Session->setFlash(__("The $singularHumanName could not be saved. Please, try again.", true), 'webadmin_flash_bad');
            }
        } else {
            $this->data = $this->$modelClass->read(null, $id);
        }

        if ($isTree) {
            $this->_setBreadcrumbData($id, false);
        }
        $this->_setAssociatedLists();
        $this->_setEnumLists();
        $this->_setModelInfo();
        $this->_setHiddenFields();

        $this->pageTitle = __('Edit ', true) . $singularHumanName . ' - ' . $this->data[$modelClass][$displayField];

    }

    /**
    * Return the itinerary from the url slug
    *
    * @return array
    **/
    protected function _findBySlug($slug)
    {
        $findBySlug = function () use ($slug) {
            return $this->Accommodation->getBySlug($slug);
        };

        return $this->cacher(implode('_', array(
            'accommodation', $slug
        )), $findBySlug);
    }
}
